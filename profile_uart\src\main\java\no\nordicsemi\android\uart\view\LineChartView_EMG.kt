/**
 * EMG折线图视图
 *
 * 使用MPAndroidChart库实现的实时数据图表：
 * 1. 图表配置：
 *    - 单Y轴显示数据
 *    - X轴显示时间（mm:ss格式）
 *    - 支持缩放和拖动
 *    - 自适应暗色/亮色主题
 *
 * 2. 数据显示：
 *    - 单一数据线条
 *    - 实时数据更新
 *    - 显示2秒数据窗口
 *
 * 3. 交互特性：
 *    - 支持触摸操作
 *    - 支持缩放查看
 *    - 自动调整坐标轴范围
 *
 * 4. 性能优化：
 *    - 高效的数据更新机制
 *    - 平滑的动画效果
 *    - 内存优化的数据结构
 */

package no.nordicsemi.android.uart.view

import android.content.Context
import android.graphics.Color
import android.graphics.DashPathEffect
import android.util.Log
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import com.github.mikephil.charting.charts.LineChart
import com.github.mikephil.charting.components.XAxis
import com.github.mikephil.charting.data.Entry
import com.github.mikephil.charting.data.LineData
import com.github.mikephil.charting.data.LineDataSet
import com.github.mikephil.charting.formatter.*
import com.github.mikephil.charting.interfaces.datasets.ILineDataSet
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.concurrent.ConcurrentLinkedQueue

private const val MAX_TIME_WINDOW = 1  // 1秒
private const val POINTS_PER_SECOND = 2000 // 2000Hz采样率
private const val VISIBLE_DATA_POINTS = MAX_TIME_WINDOW * POINTS_PER_SECOND // 2000点
private const val Y_AXIS_MIN = 5000f  // Y轴最小值
private const val Y_AXIS_MAX = 10000f // Y轴最大值

@Composable
internal fun LineChartView_EMG(
    dataPoints: List<Int>,
    label: String,
    color: Int,
    zoomIn: Boolean,
    modifier: Modifier = Modifier
) {
    val isSystemInDarkTheme = isSystemInDarkTheme()

    // 添加时间计数器
    val totalPointsCounter = remember {
        mutableStateOf(0L)
    }

    // 添加数据接收日志，包含图表标识
//    Log.d("LineChartView_EMG", "[${label}] 接收到新数据: size=${dataPoints.size}, " +
//            "前5个数据点=${dataPoints.take(5)}, " +
//            "后5个数据点=${dataPoints.takeLast(5)}")

    // 缓存图表实例和标签
    val chart = remember {
        mutableStateOf<LineChart?>(null)
    }
    val chartLabel = remember {
        mutableStateOf(label)
    }

    // 使用remember缓存更新时间
    val lastUpdateTime = remember {
        mutableStateOf(0L)
    }

    // 创建协程作用域
    val scope = rememberCoroutineScope()

    // 创建图表视图
    AndroidView(
        modifier = modifier
            .fillMaxWidth()
            .height(300.dp),
        factory = { context ->
//            Log.d("LineChartView_EMG", "[${chartLabel.value}] 创建图表实例")
            createLineChartView_EMG(isSystemInDarkTheme, context, dataPoints, chartLabel.value, color, zoomIn).also {
                chart.value = it
            }
        },
        update = { view ->
            // 只保留时间间隔检查
            val currentTime = System.currentTimeMillis()
            if (currentTime - lastUpdateTime.value >= 200) { // 200ms最小更新间隔
//                Log.d("LineChartView_EMG", "[${chartLabel.value}] 更新图表数据，数据点数量: ${dataPoints.size}")

                lastUpdateTime.value = currentTime

                // 使用单一协程更新图表
                scope.launch(Dispatchers.Default) {
                    // 计算当前数据点的起始时间
                    val startTimeInSeconds = totalPointsCounter.value.toFloat() / POINTS_PER_SECOND
                    updateChartData(view, dataPoints, chartLabel.value, color, zoomIn, startTimeInSeconds)
                    // 更新总点数
                    totalPointsCounter.value += dataPoints.size
                }
            }
        }
    )

    // 使用LaunchedEffect处理清理工作
    DisposableEffect(Unit) {
        onDispose {
//            Log.d("LineChartView_EMG", "[${chartLabel.value}] 清理图表资源")
            chart.value?.clear()
        }
    }
}

private suspend fun updateChartData(
    chart: LineChart,
    dataPoints: List<Int>,
    label: String,
    color: Int,
    zoomIn: Boolean,
    startTimeInSeconds: Float
) = withContext(Dispatchers.Default) {
    try {
        // 添加更详细的数据点信息日志
        val timeRange = dataPoints.size.toFloat() / POINTS_PER_SECOND
//        Log.d("LineChartView_EMG", """
//             [$label] 图表数据更新详情:
//             - 数据点数量: ${dataPoints.size}
//             - 理论所需点数: ${POINTS_PER_SECOND * MAX_TIME_WINDOW}
//             - 计算得到的时间范围: ${timeRange}秒
//             - 起始时间: ${startTimeInSeconds}秒
//             - 前5个数据点时间: ${dataPoints.take(5).mapIndexed { i, v ->
//            String.format("%.3fs", startTimeInSeconds + i.toFloat() / POINTS_PER_SECOND)
//        }}
//             - 后5个数据点时间: ${dataPoints.takeLast(5).mapIndexed { i, v ->
//            String.format("%.3fs", startTimeInSeconds + (dataPoints.size - 5 + i).toFloat() / POINTS_PER_SECOND)
//        }}
//         """.trimIndent())
//
//        Log.d("LineChartView_EMG", "[$label] 开始更新图表数据，数据点数量: ${dataPoints.size}")
//        Log.d("LineChartView_EMG", "[$label] EntryPool stats before update: ${EntryPool.getStats()}")

        // 在后台线程准备数据
        val entries = ArrayList<Entry>(dataPoints.size)
        dataPoints.forEachIndexed { i, v ->
            // 将时间映射到0-2秒范围内
            val timeInSeconds = (i.toFloat() / POINTS_PER_SECOND) % MAX_TIME_WINDOW.toFloat()
            entries.add(EntryPool.acquire(timeInSeconds, v.toFloat()))
        }

        val dataSet = createLineDataSet(entries, label, chart.isDarkTheme(), color)
        val dataSets = ArrayList<ILineDataSet>().apply {
            add(dataSet)
        }

        // 在主线程更新图表
        withContext(Dispatchers.Main) {
//            Log.d("LineChartView_EMG", "[$label] 在主线程更新图表")
            chart.apply {
                // 释放旧的Entry对象
                data?.let { oldData ->
                    oldData.dataSets.forEach { dataset ->
                        if (dataset is LineDataSet) {
                            val oldEntries = dataset.values
                            if (oldEntries != null) {
                                EntryPool.releaseAll(oldEntries)
                            }
                        }
                    }
                }

                data = LineData(dataSets)

                axisLeft.apply {
                    axisMaximum = Y_AXIS_MAX
                    axisMinimum = Y_AXIS_MIN
                }

                // 设置固定的X轴范围（0-2秒）
                xAxis.apply {
                    axisMinimum = 0f
                    axisMaximum = MAX_TIME_WINDOW.toFloat()
                }

                // 固定显示范围为2秒
                setVisibleXRangeMaximum(MAX_TIME_WINDOW.toFloat())
                setVisibleXRangeMinimum(MAX_TIME_WINDOW.toFloat())

                // 保持视图固定在0-2秒范围
                moveViewToX(0f)


            }
        }

//        Log.d("LineChartView_EMG", "[$label] EntryPool stats after update: ${EntryPool.getStats()}")
    } catch (e: Exception) {
//        Log.e("LineChartView_EMG", "[$label] 更新图表数据失败", e)
    }
}

private fun createLineChartView_EMG(
    isDarkTheme: Boolean,
    context: Context,
    dataPoints: List<Int>,
    label: String,
    color: Int,
    zoomIn: Boolean
): LineChart {
    return LineChart(context).apply {
        description.isEnabled = false
        legend.isEnabled = false

        setTouchEnabled(true)
        setDragEnabled(true)
        setScaleEnabled(true)
        setPinchZoom(true)

        // 设置主题颜色
        setupThemeColors(isDarkTheme)

        // 配置X轴
        setupXAxis()

        // 配置Y轴
        setupYAxis(dataPoints, zoomIn)

        // 禁用右侧Y轴
        axisRight.isEnabled = false

        // 初始化数据
        val entries = dataPoints.mapIndexed { i, v ->
            // 将索引转换为时间（秒）
            val timeInSeconds = (i.toFloat() / POINTS_PER_SECOND)
            Entry(timeInSeconds, v.toFloat())
        }

        val dataSet = createLineDataSet(entries, label, isDarkTheme, color)
        val dataSets = ArrayList<ILineDataSet>().apply {
            add(dataSet)
        }

        data = LineData(dataSets)
    }
}

private fun LineChart.setupThemeColors(isDarkTheme: Boolean) {
    if (isDarkTheme) {
        setBackgroundColor(Color.TRANSPARENT)
        xAxis.gridColor = Color.WHITE
        xAxis.textColor = Color.WHITE
        axisLeft.gridColor = Color.WHITE
        axisLeft.textColor = Color.WHITE
    } else {
        setBackgroundColor(Color.WHITE)
        xAxis.gridColor = Color.BLACK
        xAxis.textColor = Color.BLACK
        axisLeft.gridColor = Color.BLACK
        axisLeft.textColor = Color.BLACK
    }
}

private fun LineChart.setupXAxis() {
    xAxis.apply {
        enableGridDashedLine(10f, 10f, 0f)
        // 初始显示1s范围
        axisMinimum = 0f
        axisMaximum = 1f  // 显示1秒数据
        setAvoidFirstLastClipping(true)
        position = XAxis.XAxisPosition.BOTTOM

        valueFormatter = object : ValueFormatter() {
            override fun getFormattedValue(value: Float): String {
                return String.format("%.1fs", value)  // 显示一位小数的秒
            }
        }
    }
}

private fun LineChart.setupYAxis(dataPoints: List<Int>, zoomIn: Boolean) {
    axisLeft.apply {
        enableGridDashedLine(10f, 10f, 0f)
        axisMaximum = Y_AXIS_MAX
        axisMinimum = Y_AXIS_MIN
    }
}

private fun createLineDataSet(
    entries: List<Entry>,
    label: String,
    isDarkTheme: Boolean,
    lineColor: Int
): LineDataSet {
    return LineDataSet(entries, label).apply {
        setDrawIcons(false)
        setDrawValues(false)
        setDrawCircles(false)
        mode = LineDataSet.Mode.CUBIC_BEZIER

        color = lineColor
        lineWidth = 1.5f

        formLineWidth = 1f
        formLineDashEffect = DashPathEffect(floatArrayOf(10f, 5f), 0f)
        formSize = 15f
        valueTextSize = 9f

        enableDashedHighlightLine(10f, 5f, 0f)
    }
}

private fun LineChart.isDarkTheme(): Boolean {
    return xAxis.textColor == Color.WHITE
}

private fun List<Int>.getMax(zoomIn: Boolean): Float {
    if (isEmpty()) return 100f
    val max = maxOrNull()!!
    return if (zoomIn) (max * 1.2f) else (max * 1.5f)
}

private fun List<Int>.getMin(zoomIn: Boolean): Float {
    if (isEmpty()) return 0f
    val min = minOrNull()!!
    return if (zoomIn) (min * 0.8f) else (min * 0.5f)
}

