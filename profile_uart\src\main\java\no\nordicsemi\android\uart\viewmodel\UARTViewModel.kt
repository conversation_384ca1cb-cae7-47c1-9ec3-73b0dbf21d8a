/**
 * UART视图模型
 *
 * 管理UART界面的业务逻辑和数据状态：
 * 1. 数据管理：
 *    - 配置数据的CRUD操作
 *    - 宏命令的管理
 *    - 通信记录的维护
 *
 * 2. 状态控制：
 *    - 连接状态管理
 *    - UI状态更新
 *    - 错误处理
 *
 * 3. 业务逻辑：
 *    - 命令执行
 *    - 数据解析
 *    - 配置同步
 *
 * 4. 生命周期：
 *    - 视图模型初始化
 *    - 资源清理
 *    - 状态保存和恢复
 *
 * 5. 依赖注入：
 *    - 仓库层注入
 *    - 服务层注入
 *    - 工具类注入
 */
package no.nordicsemi.android.uart.viewmodel

import android.os.ParcelUuid
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.launch
import no.nordicsemi.android.analytics.AppAnalytics
import no.nordicsemi.android.analytics.Profile
import no.nordicsemi.android.analytics.ProfileConnectedEvent
import no.nordicsemi.android.analytics.UARTChangeConfiguration
import no.nordicsemi.android.analytics.UARTCreateConfiguration
import no.nordicsemi.android.analytics.UARTMode
import no.nordicsemi.android.analytics.UARTSendAnalyticsEvent
import no.nordicsemi.android.common.navigation.NavigationResult
import no.nordicsemi.android.common.navigation.Navigator
import no.nordicsemi.android.kotlin.ble.core.data.GattConnectionState
import no.nordicsemi.android.toolbox.scanner.ScannerDestinationId
import no.nordicsemi.android.toolbox.scanner.SelectedDevice
import no.nordicsemi.android.uart.data.MacroEol
import no.nordicsemi.android.uart.data.UARTConfiguration
import no.nordicsemi.android.uart.data.UARTMacro
import no.nordicsemi.android.uart.data.UARTPersistentDataSource
import no.nordicsemi.android.uart.data.UARTServiceData
import no.nordicsemi.android.uart.repository.UARTRepository
import no.nordicsemi.android.uart.repository.UART_SERVICE_UUID
import no.nordicsemi.android.uart.view.ClearOutputItems
import no.nordicsemi.android.uart.view.DisconnectEvent
import no.nordicsemi.android.uart.view.MacroInputSwitchClick
import no.nordicsemi.android.uart.view.NavigateUp
import no.nordicsemi.android.uart.view.OnAddConfiguration
import no.nordicsemi.android.uart.view.OnConfigurationSelected
import no.nordicsemi.android.uart.view.OnCreateMacro
import no.nordicsemi.android.uart.view.OnDeleteConfiguration
import no.nordicsemi.android.uart.view.OnDeleteMacro
import no.nordicsemi.android.uart.view.OnEditConfiguration
import no.nordicsemi.android.uart.view.OnEditFinish
import no.nordicsemi.android.uart.view.OnEditMacro
import no.nordicsemi.android.uart.view.OnRunInput
import no.nordicsemi.android.uart.view.OnRunMacro
import no.nordicsemi.android.uart.view.OpenLogger
import no.nordicsemi.android.uart.view.UARTViewEvent
import no.nordicsemi.android.uart.view.UARTViewState
import no.nordicsemi.android.uart.view.SwitchZoomEvent

import javax.inject.Inject

@HiltViewModel
internal class UARTViewModel @Inject constructor(
    private val repository: UARTRepository,// 数据仓库层
    private val navigationManager: Navigator,// 导航管理器
    private val dataSource: UARTPersistentDataSource,// 持久化数据源
    private val analytics: AppAnalytics,// 分析统计
) : ViewModel() {
    private val _state = MutableStateFlow(UARTViewState())// 状态流
    val state = _state.asStateFlow()// 状态流

    /*
    // UI性能监控相关变量
    private var lastUiUpdateTime = System.currentTimeMillis()
    private var uiUpdateCounter = 0
    private var lastMemoryLogTime = 0L
    private val MEMORY_LOG_INTERVAL = 5000L // 内存日志记录间隔(5秒)
    private var lastStateValue: UARTServiceData? = null // 用于检测状态是否真的改变

    // 监控UI更新频率和性能
    private fun monitorUiUpdate() {
        uiUpdateCounter++
        val currentTime = System.currentTimeMillis()
        if (currentTime - lastUiUpdateTime >= 1000) {
            val duration = (currentTime - lastUiUpdateTime) / 1000.0
            val frequency = uiUpdateCounter / duration

            // 获取内存信息
            val runtime = Runtime.getRuntime()
            val usedMemory = (runtime.totalMemory() - runtime.freeMemory()) / 1024 / 1024
            val totalMemory = runtime.totalMemory() / 1024 / 1024
            val maxMemory = runtime.maxMemory() / 1024 / 1024

//            android.util.Log.d("UI_PERFORMANCE", """UI更新统计:
//                |更新频率: $frequency 次/秒
//                |已用内存: ${usedMemory}MB
//                |总内存: ${totalMemory}MB
//                |最大可用内存: ${maxMemory}MB
//                |内存使用率: ${(usedMemory.toFloat() / maxMemory * 100).toInt()}%
//            """.trimMargin())

            // 重置计数器
            uiUpdateCounter = 0
            lastUiUpdateTime = currentTime
        }
    }

    // 监控内存使用情况
    private fun monitorMemoryUsage() {
        val currentTime = System.currentTimeMillis()
        if (currentTime - lastMemoryLogTime >= MEMORY_LOG_INTERVAL) {
            val runtime = Runtime.getRuntime()
            val usedMemory = (runtime.totalMemory() - runtime.freeMemory()) / 1024 / 1024

            // 设置内存警告阈值（比如使用超过80%）
            val memoryUsagePercent = (usedMemory.toFloat() / runtime.maxMemory() * 100 * 1024 * 1024).toInt()
            if (memoryUsagePercent > 80) {
//                android.util.Log.w("UI_MEMORY_WARNING", """内存使用警告:
//                    |已用内存: ${usedMemory}MB
//                    |内存使用率: $memoryUsagePercent%
//                    |建议进行内存优化
//                """.trimMargin())
            }

            lastMemoryLogTime = currentTime
        }
    }
    */

    init {
        repository.setOnScreen(true)// 设置界面可见状态
        // 检查运行状态，如果未运行则请求蓝牙设备
        viewModelScope.launch {
            if (repository.isRunning.firstOrNull() == false) {
                requestBluetoothDevice()// 请求蓝牙设备
            }
        }

        // 收集UART数据Flow
        repository.data
            .onEach { newState ->
                _state.value = _state.value.copy(
                    uartManagerState = newState,
                    latestBodyValue = newState.bodyMessages.lastOrNull(),
                    latestAmbientValue = newState.ambientMessages.lastOrNull(),
                    latestEmgValue = newState.emgMessages.lastOrNull(),
                )

                // 记录性能日志
//                android.util.Log.d("UI_PERFORMANCE", """状态更新:
//                    |Body值: ${newState.bodyMessages.lastOrNull()}
//                    |Ambient值: ${newState.ambientMessages.lastOrNull()}
//                    |EMG值: ${newState.emgMessages.lastOrNull()}
//                """.trimMargin())

                if (newState.connectionState?.state == GattConnectionState.STATE_CONNECTED) {
                    analytics.logEvent(ProfileConnectedEvent(Profile.UART))
                }
            }
            .launchIn(viewModelScope)

        dataSource.getConfigurations().onEach {
//            val startTime = System.nanoTime()
            _state.value = _state.value.copy(configurations = it)
//            val updateTime = (System.nanoTime() - startTime) / 1000000.0
//            android.util.Log.d("UI_PERFORMANCE", "配置更新耗时: ${updateTime}ms")
        }.launchIn(viewModelScope)

        repository.lastConfigurationName.onEach {
//            val startTime = System.nanoTime()
            it?.let {
                _state.value = _state.value.copy(selectedConfigurationName = it)
            }
//            val updateTime = (System.nanoTime() - startTime) / 1000000.0
//            android.util.Log.d("UI_PERFORMANCE", "配置名称更新耗时: ${updateTime}ms")
        }.launchIn(viewModelScope)
    }

    private fun requestBluetoothDevice() {
        navigationManager.navigateTo(ScannerDestinationId, ParcelUuid(UART_SERVICE_UUID))

        navigationManager.resultFrom(ScannerDestinationId)
            .onEach { handleResult(it) }
            .launchIn(viewModelScope)
    }

    internal fun handleResult(result: NavigationResult<SelectedDevice>) {
        when (result) {
            is NavigationResult.Cancelled -> navigationManager.navigateUp()
            is NavigationResult.Success -> onDeviceSelected(result.value)
        }
    }

    private fun onDeviceSelected(device: SelectedDevice) {
        repository.launch(device)
    }

    fun onEvent(event: UARTViewEvent) {
        when (event) {
            is OnCreateMacro -> addNewMacro(event.macro)
            OnDeleteMacro -> deleteMacro()
            DisconnectEvent -> disconnect()
            is OnRunMacro -> runMacro(event.macro)
            NavigateUp -> navigationManager.navigateUp()
            is OnEditMacro -> onEditMacro(event)
            OnEditFinish -> onEditFinish()
            is OnConfigurationSelected -> onConfigurationSelected(event)
            is OnAddConfiguration -> onAddConfiguration(event)
            OnDeleteConfiguration -> deleteConfiguration()
            OnEditConfiguration -> onEditConfiguration()
            ClearOutputItems -> repository.clearItems()
            OpenLogger -> repository.openLogger()
            is OnRunInput -> sendText(event.text, event.newLineChar)
            MacroInputSwitchClick -> onMacroInputSwitch()

            //修改
            SwitchZoomEvent -> onZoomButtonClicked()
        }
    }
    //缩放功能
    private fun onZoomButtonClicked() {
        repository.switchZoomIn()
    }
    //运行宏
    private fun runMacro(macro: UARTMacro) {
        repository.runMacro(macro)
        analytics.logEvent(UARTSendAnalyticsEvent(UARTMode.MACRO))
    }
    //发送文本
    private fun sendText(text: String, newLineChar: MacroEol) {
        repository.sendText(text, newLineChar)
        analytics.logEvent(UARTSendAnalyticsEvent(UARTMode.TEXT))
    }
    //宏输入开关
    private fun onMacroInputSwitch() {
        _state.value = _state.value.copy(isInputVisible = !state.value.isInputVisible)
    }
    //编辑配置
    private fun onEditConfiguration() {
        val isEdited = _state.value.isConfigurationEdited
        _state.value = _state.value.copy(isConfigurationEdited = !isEdited)
    }
    //添加配置
    private fun onAddConfiguration(event: OnAddConfiguration) {
        viewModelScope.launch(Dispatchers.IO) {
            dataSource.saveConfiguration(UARTConfiguration(null, event.name))
            _state.value = _state.value.copy(selectedConfigurationName = event.name)
        }
        saveLastConfigurationName(event.name)
        analytics.logEvent(UARTCreateConfiguration())
    }
    //编辑宏
    private fun onEditMacro(event: OnEditMacro) {
        _state.value = _state.value.copy(editedPosition = event.position)
    }
    //编辑完成
    private fun onEditFinish() {
        _state.value = _state.value.copy(editedPosition = null)
    }
    //配置选择
    private fun onConfigurationSelected(event: OnConfigurationSelected) {
        saveLastConfigurationName(event.configuration.name)
        analytics.logEvent(UARTChangeConfiguration())
    }
    //保存最后配置名称
    private fun saveLastConfigurationName(name: String) {
        viewModelScope.launch {
            repository.saveConfigurationName(name)
        }
    }
    //添加新宏
    private fun addNewMacro(macro: UARTMacro) {
        viewModelScope.launch(Dispatchers.IO) {
            _state.value.selectedConfiguration?.let {
                val macros = it.macros.toMutableList().apply {
                    set(_state.value.editedPosition!!, macro)
                }
                val newConf = it.copy(macros = macros)
                dataSource.saveConfiguration(newConf)
                _state.value = _state.value.copy(editedPosition = null)
            }
        }
    }
    //删除配置  
    private fun deleteConfiguration() {
        viewModelScope.launch(Dispatchers.IO) {
            _state.value.selectedConfiguration?.let {
                dataSource.deleteConfiguration(it)
            }
        }
    }
    //删除宏
    private fun deleteMacro() {
        viewModelScope.launch(Dispatchers.IO) {
            _state.value.selectedConfiguration?.let {
                val macros = it.macros.toMutableList().apply {
                    set(_state.value.editedPosition!!, null)
                }
                val newConf = it.copy(macros = macros)
                dataSource.saveConfiguration(newConf)
                _state.value = _state.value.copy(editedPosition = null)
            }
        }
    }
    //断开连接
    private fun disconnect() {
        repository.disconnect()
        navigationManager.navigateUp()
    }
    //清除
    override fun onCleared() {
        super.onCleared()
        repository.setOnScreen(false)
    }
}
