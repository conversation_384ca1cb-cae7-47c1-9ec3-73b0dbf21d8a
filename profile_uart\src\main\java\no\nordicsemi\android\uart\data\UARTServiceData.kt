package no.nordicsemi.android.uart.data

import android.util.Log
import kotlinx.coroutines.*
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.*
import no.nordicsemi.android.kotlin.ble.core.data.BleGattConnectionStatus
import no.nordicsemi.android.kotlin.ble.core.data.GattConnectionStateWithStatus
import kotlin.time.Duration.Companion.milliseconds
import kotlin.math.sqrt

private class CircularBuffer<T>(private val capacity: Int) {
    private val buffer = Array<Any?>(capacity) { null }
    private var writeIndex = 0
    private var size = 0
    private var lastReadIndex = 0

    fun add(element: T) {
        buffer[writeIndex] = element
        writeIndex = (writeIndex + 1) % capacity
        size = minOf(size + 1, capacity)
    }

    fun addAll(elements: Collection<T>) {
        elements.forEach { add(it) }
    }

    fun getAll(): List<T> = getLatest(size)

    fun getNewData(): List<T> {
        if (size == 0) return emptyList()

        val result = mutableListOf<T>()
        var currentIndex = lastReadIndex

        while (currentIndex != writeIndex) {
            buffer[currentIndex]?.let { result.add(it as T) }
            currentIndex = (currentIndex + 1) % capacity
        }

        lastReadIndex = writeIndex

        return result
    }

    fun getLatest(count: Int): List<T> {
        if (size == 0) return emptyList()

        val result = mutableListOf<T>()
        var readIndex = if (writeIndex == 0) capacity - 1 else writeIndex - 1

        repeat(minOf(count, size)) {
            @Suppress("UNCHECKED_CAST")
            buffer[readIndex]?.let { result.add(it as T) }
            readIndex = if (readIndex == 0) capacity - 1 else readIndex - 1
        }
        return result.reversed()
    }

    fun clear() {
        buffer.fill(null)
        writeIndex = 0
        size = 0
        lastReadIndex = 0
    }

    fun size() = size

    fun isEmpty() = size == 0
}

private object UARTBufferManager {
    private const val RAW_BUFFER_SIZE = 2000


    // 原始数据缓冲区
    private val rawChannel1Buffer = CircularBuffer<Int>(RAW_BUFFER_SIZE)
    private val rawChannel2Buffer = CircularBuffer<Int>(RAW_BUFFER_SIZE)
    private val rawChannel3Buffer = CircularBuffer<Int>(RAW_BUFFER_SIZE)

    fun addBodyData(data: Int) = rawChannel1Buffer.add(data)
    fun addAmbientData(data: Int) = rawChannel2Buffer.add(data)
    fun addEmgData(data: Int) = rawChannel3Buffer.add(data)

    fun getNewBodyData(): List<Int> = rawChannel1Buffer.getNewData()
    fun getNewAmbientData(): List<Int> = rawChannel2Buffer.getNewData()
    fun getNewEmgData(): List<Int> = rawChannel3Buffer.getNewData()

    fun getAllBodyData(): List<Int> = rawChannel1Buffer.getAll()
    fun getAllAmbientData(): List<Int> = rawChannel2Buffer.getAll()
    fun getAllEmgData(): List<Int> = rawChannel3Buffer.getAll()

    fun clearAll() {
        rawChannel1Buffer.clear()
        rawChannel2Buffer.clear()
        rawChannel3Buffer.clear()
    }
}

internal data class UARTServiceData(
    val messages: List<UARTRecord> = emptyList(),
    val connectionState: GattConnectionStateWithStatus? = null,
    val batteryLevel: Int? = null,
    val deviceName: String? = null,
    val missingServices: Boolean = false,
    val bodySensorLocation: Int? = null,
    val zoomIn: Boolean = false,
) {
    private val TAG = "UARTServiceData"

    val disconnectStatus = if (missingServices) {
        BleGattConnectionStatus.NOT_SUPPORTED
    } else {
        connectionState?.status ?: BleGattConnectionStatus.UNKNOWN
    }

    companion object {
        private const val MIN_UPDATE_INTERVAL = 100L
    }

    private val scope = CoroutineScope(SupervisorJob() + Dispatchers.Default)
    private val messagesFlow = MutableStateFlow(messages)

    //数据接收处理
    suspend fun receiveData(data: String) {
        withContext(Dispatchers.Default) {
            try {
                // 在Default调度器上执行CPU密集型的解析操作
                val parsedValues = parseRawData(data)

                // 在IO调度器上执行数据处理和存储
                withContext(Dispatchers.IO) {
                    processValues(parsedValues)
                }
            } catch (e: Exception) {
//                Log.e("UART_DEBUG", "数据处理错误: ${e.message}")
            }
        }
    }

    /**
     * 将两个字节转换为一个整数
     *
     * @param highByte 高字节
     * @param lowByte 低字节
     * @return 转换后的整数值
     */
    private fun bytesToInt(highByte: Byte, lowByte: Byte): Int {
        return (highByte.toInt() and 0xFF) * 256 + (lowByte.toInt() and 0xFF)
    }

    /**
     * 格式化数字为5位字符串
     *
     * 将数字格式化为固定5位的字符串,不足位数在前面补0
     *
     * @param number 要格式化的数字
     * @return 格式化后的5位字符串
     */
    private fun formatNumber(number: Int): String {
        return when {
            number < 10 -> "0000$number"
            number < 100 -> "000$number"
            number < 1000 -> "00$number"
            number < 10000 -> "0$number"
            else -> number.toString()
        }
    }

    /**
     * 解析后的双通道数据值类
     *
     * @property channel1Value 通道1的数据值
     * @property channel2Value 通道2的数据值
     */
    private data class ParsedValues(
        val channel1Value: Int,
        val channel2Value: Int
    )

    /**
     * 解析原始数据字符串
     *
     * 该函数将原始字符串数据解析为双通道数值列表:
     * 1. 将字符串转换为字节数组
     * 2. 每4个字节解析为一组双通道数据
     * 3. 返回解析后的数据列表
     *
     * @param data 原始数据字符串
     * @return 解析后的双通道数值列表
     */
    private fun parseRawData(data: String): List<ParsedValues> {
        val result = mutableListOf<ParsedValues>()
        try {
            val bytes = data.toByteArray(Charsets.ISO_8859_1)
            var i = 0
            while (i < bytes.size - 3) {
                val value1 = bytesToInt(bytes[i], bytes[i + 1])
                val value2 = bytesToInt(bytes[i + 2], bytes[i + 3])
                result.add(ParsedValues(value1, value2))
                i += 4
            }
        } catch (e: Exception) {
//            Log.e("UART_DEBUG", "数据解析错误: ${e.message}", e)
        }
        return result
    }
    /**
     * 处理解析后的双通道数据值
     *
     * 该函数将解析后的数据添加到缓冲区:
     * 1. channel1Value添加到body数据缓冲区
     * 2. channel2Value添加到ambient数据缓冲区
     *
     * @param values 解析后的双通道数据值列表
     */
    private fun processValues(values: List<ParsedValues>) {
//        Log.d("UART_DEBUG", "开始处理数据，数据包大小: ${values.size}")
        values.forEachIndexed { index, parsedValue ->
            UARTBufferManager.addBodyData(parsedValue.channel1Value)
            UARTBufferManager.addAmbientData(parsedValue.channel2Value)
//            Log.d("UART_DEBUG", "处理第${index + 1}组数据 - 体声音: ${parsedValue.channel1Value}, 环境声音: ${parsedValue.channel2Value}")
        }

        // 添加缓冲区状态日志
//        Log.d("UART_DEBUG", "当前缓冲区数据 - 体声音: ${UARTBufferManager.getAllBodyData().size}, 环境声音数据: ${UARTBufferManager.getAllAmbientData().size}")
    }


    /**
     * 将UART记录转换为显示消息
     */
    val displayMessages = messages.map { record ->
        if (record.type == UARTRecordType.INPUT) {
            record
        } else {
            // 对于输出数据，使用简单的空格分隔，不做格式化
            val parsedValues = parseRawData(record.text)
            val formattedText = buildString {
                parsedValues.forEachIndexed { index, values ->
                    if (index > 0) append(' ')
                    append(values.channel1Value)
                    append(' ')
                    append(values.channel2Value)
                }
            }
            record.copy(text = formattedText)
        }
    }

    val bodyMessages: List<Int>
        get() = UARTBufferManager.getAllBodyData()

    val ambientMessages: List<Int>
        get() = UARTBufferManager.getAllAmbientData()

    val emgMessages: List<Int>
        get() = UARTBufferManager.getAllEmgData()

    val recordBodyMessages: List<Int>
        get() = UARTBufferManager.getNewBodyData()

    val recordAmbientMessages: List<Int>
        get() = UARTBufferManager.getNewAmbientData()

    val recordEmgMessages: List<Int>
        get() = UARTBufferManager.getNewEmgData()

    fun cleanup() {
        scope.cancel()
        UARTBufferManager.clearAll()
    }
}

internal data class UARTRecord(
    val text: String,
    val type: UARTRecordType,
    val timestamp: Long = System.currentTimeMillis()
)

enum class UARTRecordType {
    INPUT, OUTPUT
}