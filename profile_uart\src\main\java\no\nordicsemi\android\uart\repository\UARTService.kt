/**
 * UART蓝牙服务实现类
 *
 * 实现了与BLE设备的UART通信服务：
 * 1. 服务特性：
 *    - UART服务UUID: 6E400001-B5A3-F393-E0A9-E50E24DCCA9E
 *    - RX特征值: 用于发送数据到设备
 *    - TX特征值: 用于接收设备数据
 *    - 可选的电池服务
 *
 * 2. 主要功能：
 *    - 建立BLE连接
 *    - 配置MTU大小
 *    - 发现并配置服务
 *    - 处理数据收发
 *    - 监控连接状态
 *
 * 3. 生命周期管理：
 *    - 服务启动和停止
 *    - 连接状态监控
 *    - 资源清理
 *
 * 4. 依赖注入：
 *    - 使用Hilt进行依赖管理
 *    - 注入UARTRepository
 */

package no.nordicsemi.android.uart.repository

import android.annotation.SuppressLint
import android.content.Intent
import androidx.core.content.IntentCompat
import androidx.lifecycle.lifecycleScope
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.mapNotNull
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import no.nordicsemi.android.kotlin.ble.client.main.callback.ClientBleGatt
import no.nordicsemi.android.kotlin.ble.client.main.service.ClientBleGattCharacteristic
import no.nordicsemi.android.kotlin.ble.client.main.service.ClientBleGattServices
import no.nordicsemi.android.kotlin.ble.core.ServerDevice
import no.nordicsemi.android.kotlin.ble.core.data.BleGattConnectionStatus
import no.nordicsemi.android.kotlin.ble.core.data.BleGattProperty
import no.nordicsemi.android.kotlin.ble.core.data.BleWriteType
import no.nordicsemi.android.kotlin.ble.core.data.GattConnectionState
import no.nordicsemi.android.kotlin.ble.core.data.Mtu
import no.nordicsemi.android.kotlin.ble.core.data.util.DataByteArray
import no.nordicsemi.android.kotlin.ble.profile.battery.BatteryLevelParser
import no.nordicsemi.android.service.DEVICE_DATA
import no.nordicsemi.android.service.NotificationService
import java.util.UUID
import javax.inject.Inject

val UART_SERVICE_UUID: UUID = UUID.fromString("6E400001-B5A3-F393-E0A9-E50E24DCCA9E")
internal val UART_RX_CHARACTERISTIC_UUID = UUID.fromString("6E400002-B5A3-F393-E0A9-E50E24DCCA9E")
internal val UART_TX_CHARACTERISTIC_UUID = UUID.fromString("6E400003-B5A3-F393-E0A9-E50E24DCCA9E")

internal val BATTERY_SERVICE_UUID = UUID.fromString("0000180F-0000-1000-8000-00805f9b34fb")
internal val BATTERY_LEVEL_CHARACTERISTIC_UUID = UUID.fromString("00002A19-0000-1000-8000-00805f9b34fb")

@SuppressLint("MissingPermission")
@AndroidEntryPoint
internal class UARTService : NotificationService() {

    @Inject
    lateinit var repository: UARTRepository

    private var client: ClientBleGatt? = null

    /**
     * 数据包统计相关变量和方法
     * 用于统计BLE通信过程中的数据包接收情况，包括:
     * - 数据包计数
     * - 总字节数
     * - 接收频率
     * - 数据速率
     * 统计周期为1秒
     */
    // 数据包统计
    /*
    private var packetCounter = 0
    private var lastStatTime = System.currentTimeMillis()
    private var totalBytes = 0L

    private fun updatePacketStats(dataSize: Int) {
        packetCounter++
        totalBytes += dataSize
        val currentTime = System.currentTimeMillis()
        // 每秒计算一次频率
        if (currentTime - lastStatTime >= 1000) {
            val duration = (currentTime - lastStatTime) / 1000.0 // 转换为秒
            val frequency = packetCounter / duration
            val bytesPerSecond = totalBytes / duration
//            android.util.Log.d("UART_STATS", """
//                数据包统计:
//                - 接收频率: $frequency 包/秒
//                - 数据量: ${totalBytes}字节/${duration}秒
//                - 平均包大小: ${totalBytes/packetCounter}字节/包
//                - 数据速率: ${bytesPerSecond}字节/秒
//            """.trimIndent())
            // 重置计数器和时间
            packetCounter = 0
            totalBytes = 0
            lastStatTime = currentTime
        }
    }
    */

    /**
     * 处理服务启动命令
     * 当服务被启动时调用此方法，主要功能包括：
     * 1. 设置服务运行状态
     * 2. 获取设备信息
     * 3. 启动GATT客户端连接
     * 4. 监听停止事件
     * 
     * @param intent 启动服务的Intent，包含设备信息
     * @param flags 启动标志
     * @param startId 启动ID
     * @return START_REDELIVER_INTENT 表示如果服务被系统杀死，将重新创建并传递相同的Intent
     */
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        super.onStartCommand(intent, flags, startId)

        repository.setServiceRunning(true)

        val device = IntentCompat.getParcelableExtra(intent!!, DEVICE_DATA, ServerDevice::class.java)!!

        startGattClient(device)

        repository.stopEvent
            .onEach { disconnect() }
            .launchIn(lifecycleScope)

        return START_REDELIVER_INTENT
    }

    /**
     * 启动GATT客户端连接
     * 此方法负责：
     * 1. 建立与BLE设备的连接
     * 2. 请求最大MTU
     * 3. 发现并配置GATT服务
     * 4. 监听连接状态变化
     * 
     * @param device 要连接的BLE设备
     */
    private fun startGattClient(device: ServerDevice) = lifecycleScope.launch {
        val client = ClientBleGatt.connect(this@UARTService, device, lifecycleScope)
        <EMAIL> = client

        if (!client.isConnected) {
            return@launch
        }

        try {
            client.requestMtu(Mtu.max)
        } catch (e: Exception) {
            e.printStackTrace()
        }

        try {
            val services = client.discoverServices()
            configureGatt(services)
        } catch (e: Exception) {
            repository.onMissingServices()
        }

        client.connectionStateWithStatus
            .filterNotNull()
            .onEach { repository.onConnectionStateChanged(it) }
            .onEach { stopIfDisconnected(it.state, it.status) }
            .filterNotNull()
            .launchIn(lifecycleScope)
    }

    /**
     * 配置GATT服务和特征值
     * 此方法负责：
     * 1. 获取UART服务和特征值
     * 2. 配置数据接收通知
     * 3. 处理数据发送
     * 4. 配置可选的电池服务
     * 
     * @param services 发现的GATT服务集合
     */
    private suspend fun configureGatt(services: ClientBleGattServices) {
        val uartService = services.findService(UART_SERVICE_UUID)!!
        val rxCharacteristic = uartService.findCharacteristic(UART_RX_CHARACTERISTIC_UUID)!!
        val txCharacteristic = uartService.findCharacteristic(UART_TX_CHARACTERISTIC_UUID)!!

        // 处理接收到的数据
        txCharacteristic.getNotifications()
            .onEach { data ->
                // 更新数据包统计
                //updatePacketStats(data.value.size)

                // 打印原始字节数组
                // val hexString = data.value.joinToString("-") { String.format("%02X", it) }
//                android.util.Log.d("UART_RAW", "接收到的原始数据: $hexString")
//                android.util.Log.d("UART_RAW", "数据长度: ${data.value.size}")

//                // 检查是否是命令回显
//                val isCommand = data.value.size == 2 || data.value.size == 3
//                if (isCommand) {
//                    val text = String(data.value)
//                    if (text.trim().matches(Regex("on(\\r|\\n|\\r\\n)?|off(\\r|\\n|\\r\\n)?"))) {
//                        android.util.Log.d("UART_RAW", "收到命令回显: $text")
//                        repository.onNewMessageReceived(text)
//                        return@onEach
//                    }
//                }

                // 处理ADC数据
                if (data.value.size >= 4 && data.value.size % 4 == 0) {
                    // 直接使用原始字节数组，不进行字符串转换
                    val rawBytes = ByteArray(data.value.size)
                    data.value.copyInto(rawBytes)
                    val rawData = String(rawBytes, Charsets.ISO_8859_1)
//                    android.util.Log.d("UART_RAW", "收到ADC数据，长度: ${data.value.size}")
//                    android.util.Log.d("UART_RAW", "发送到UARTServiceData的数据: ${rawBytes.joinToString("-") { String.format("%02X", it) }}")
                    repository.onNewMessageReceived(rawData)
                } else {
//                    android.util.Log.d("UART_RAW", "收到未知格式数据")
                }
            }
            .catch { it.printStackTrace() }
            .launchIn(lifecycleScope)

        // 处理发送的命令
        repository.command
            .onEach { command ->
//                android.util.Log.d("UART_RAW", "发送命令: $command")
                rxCharacteristic.splitWrite(DataByteArray.from(command), getWriteType(rxCharacteristic))
                repository.onNewMessageSent(command)
            }
            .catch { it.printStackTrace() }
            .launchIn(lifecycleScope)

        // Battery service is optional
        services.findService(BATTERY_SERVICE_UUID)
            ?.findCharacteristic(BATTERY_LEVEL_CHARACTERISTIC_UUID)
            ?.getNotifications()
            ?.mapNotNull { BatteryLevelParser.parse(it) }
            ?.onEach { repository.onBatteryLevelChanged(it) }
            ?.catch { it.printStackTrace() }
            ?.launchIn(lifecycleScope)
    }

    /**
     * 获取BLE写入类型
     * @param characteristic BLE特征值
     * @return 写入类型：如果特征值支持写入则返回DEFAULT，否则返回NO_RESPONSE
     */
    private fun getWriteType(characteristic: ClientBleGattCharacteristic): BleWriteType {
        return if (characteristic.properties.contains(BleGattProperty.PROPERTY_WRITE)) {
            BleWriteType.DEFAULT
        } else {
            BleWriteType.NO_RESPONSE
        }
    }

    /**
     * 处理断开连接的情况
     * @param connectionState 连接状态
     * @param connectionStatus 连接状态详情
     */
    private fun stopIfDisconnected(connectionState: GattConnectionState, connectionStatus: BleGattConnectionStatus) {
        if (connectionState == GattConnectionState.STATE_DISCONNECTED && !connectionStatus.isLinkLoss) {
            repository.disconnect()
            stopSelf()
        }
    }

    /**
     * 断开BLE连接
     */
    private fun disconnect() {
        client?.disconnect()
    }

    /**
     * 服务销毁时的处理
     */
    override fun onDestroy() {
        super.onDestroy()
        repository.setServiceRunning(false)
    }
}
